"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Brain, Cpu, GraduationCap, ArrowRight, 
  TrendingUp, Users, Clock, Target 
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useTranslations, useRawTranslations } from '@/hooks/useTranslations'

export default function CasesClient() {
  const t = useTranslations('cases')
  const rawData = useRawTranslations('cases')

  // 图标映射
  const iconMap = {
    aiAnnotation: Brain,
    cpuRental: Cpu,
    educationManagement: GraduationCap
  }

  // 统计数据
  const stats = [
    { labelKey: "stats.successfulCases", value: "200+", icon: Target },
    { labelKey: "stats.clientsServed", value: "500+", icon: Users },
    { labelKey: "stats.completionRate", value: "99.8%", icon: TrendingUp },
    { labelKey: "stats.satisfaction", value: "98%", icon: Clock }
  ]

  // 案例数据
  const cases = rawData.cases || []

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-24 sm:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10" />
        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl font-bold tracking-tight sm:text-6xl"
            >
              {t('title')}
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mt-6 text-lg leading-8 text-muted-foreground"
            >
              {t('subtitle')}
            </motion.p>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.labelKey}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="mx-auto mb-4 rounded-lg bg-primary/10 p-3 w-fit">
                  <stat.icon className="h-8 w-8 text-primary" />
                </div>
                <div className="text-3xl font-bold text-primary">{stat.value}</div>
                <div className="mt-2 text-sm text-muted-foreground">{t(stat.labelKey)}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Cases Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid gap-8 lg:grid-cols-2">
            {Array.isArray(cases) && cases.map((caseItem: any, index: number) => {
              const IconComponent = iconMap[caseItem.category as keyof typeof iconMap] || Brain
              const categoryLabel = t(`categories.${caseItem.category}`)
              
              return (
                <motion.div
                  key={caseItem.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="h-full overflow-hidden group hover:shadow-lg transition-shadow duration-300">
                    <div className="aspect-[3/2] relative overflow-hidden">
                      <Image
                        src={`https://picsum.photos/seed/${caseItem.id}/600/400`}
                        alt={caseItem.title}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute top-4 left-4">
                        <Badge variant="secondary" className="bg-background/80 backdrop-blur-sm">
                          {categoryLabel}
                        </Badge>
                      </div>
                    </div>
                    <CardHeader>
                      <div className="flex items-start gap-4">
                        <div className="rounded-lg bg-primary/10 p-2">
                          <IconComponent className="h-6 w-6 text-primary" />
                        </div>
                        <div className="flex-1">
                          <CardTitle className="text-xl">{caseItem.title}</CardTitle>
                          <CardDescription className="mt-1">{caseItem.client}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-sm text-muted-foreground mb-2">{t('labels.challenge')}</h4>
                        <p className="text-sm">{caseItem.challenge}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm text-muted-foreground mb-2">{t('labels.solution')}</h4>
                        <p className="text-sm">{caseItem.solution}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm text-muted-foreground mb-2">{t('labels.results')}</h4>
                        <ul className="text-sm space-y-1">
                          {caseItem.results.map((result: string, idx: number) => (
                            <li key={idx} className="flex items-center gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-primary" />
                              {result}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div className="flex flex-wrap gap-2 pt-2">
                        {caseItem.tags.map((tag: string) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 sm:py-32 bg-primary/5">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              {t('cta.title')}
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              {t('cta.subtitle')}
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/contact-us">
                  {t('cta.startCooperation')}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg">
                {t('cta.learnMore')}
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
