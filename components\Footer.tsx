﻿"use client"

import { Linkedin, Mail, Phone, MapPin } from 'lucide-react'
import Link from 'next/link'
import { Button } from './ui/button'
import { useTranslations } from '@/hooks/useTranslations'

const Footer = () => {
  const currentYear = new Date().getFullYear()
  const t = useTranslations('footer')
  const tCommon = useTranslations('common')
  const tNav = useTranslations('navigation')

  return (
    <footer className="bg-slate-50 border-t border-slate-200">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-2">
              <div className="flex items-center gap-2 mb-6">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">0</span>
                </div>
                <span className="text-xl font-bold text-slate-800">0dot</span>
              </div>
              <p className="text-slate-600 mb-6 leading-relaxed">
                {t('description')}
              </p>
              
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-slate-600">
                  <Phone className="h-4 w-4 text-blue-600" />
                  <span className="text-sm">************</span>
                </div>
                <div className="flex items-center gap-3 text-slate-600">
                  <Mail className="h-4 w-4 text-blue-600" />
                  <span className="text-sm"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-slate-600">
                  <MapPin className="h-4 w-4 text-blue-600" />
                  <span className="text-sm">北京市朝阳区科技园区88号零点大厦</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-slate-800 mb-4">{t('company')}</h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/about" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    {tNav('about')}
                  </Link>
                </li>
                <li>
                  <Link href="/products" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    {tNav('products')}
                  </Link>
                </li>
                <li>
                  <Link href="/news" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    {tNav('news')}
                  </Link>
                </li>
                <li>
                  <Link href="/careers" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    {t('links.careers')}
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-slate-800 mb-4">{t('support')}</h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/help" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    {t('links.helpCenter')}
                  </Link>
                </li>
                <li>
                  <Link href="/support" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    {t('links.techSupport')}
                  </Link>
                </li>
                <li>
                  <Link href="/contact-us" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    {tCommon('contact')}
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    {tCommon('privacy')}
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="border-t border-slate-200 py-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <p className="text-sm text-slate-500">
              &copy; {currentYear} {t('copyright')}
            </p>
            <div className="flex items-center gap-2">
              <span className="text-sm text-slate-500 mr-2">{t('followUs')}</span>
              <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
                <Link href="https://linkedin.com/company/0dot" target="_blank" rel="noopener noreferrer">
                  <Linkedin className="h-4 w-4" />
                  <span className="sr-only">LinkedIn</span>
                </Link>
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
                <Link href="mailto:<EMAIL>">
                  <Mail className="h-4 w-4" />
                  <span className="sr-only">{tCommon('contact')}</span>
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
